import React, { useState, useEffect } from 'react';
import { FileText, Download, Calendar, TrendingUp, Users, Activity } from 'lucide-react';
import { db } from '../database/db';

const Reports: React.FC = () => {
  const [reportType, setReportType] = useState<'attendance' | 'activities' | 'children'>('attendance');
  const [dateRange, setDateRange] = useState({
    start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    end: new Date().toISOString().split('T')[0],
  });

  const generateAttendanceReport = () => {
    const children = db.getChildren().filter(child => child.isActive);
    const attendance = db.getAttendance();
    
    const report = children.map(child => {
      const childAttendance = attendance.filter(record => 
        record.childId === child.id &&
        record.date >= dateRange.start &&
        record.date <= dateRange.end
      );
      
      const totalDays = childAttendance.length;
      const presentDays = childAttendance.filter(record => record.status === 'present').length;
      const absentDays = childAttendance.filter(record => record.status === 'absent').length;
      const lateDays = childAttendance.filter(record => record.status === 'late').length;
      const sickDays = childAttendance.filter(record => record.status === 'sick').length;
      
      return {
        childName: `${child.firstName} ${child.lastName}`,
        totalDays,
        presentDays,
        absentDays,
        lateDays,
        sickDays,
        attendanceRate: totalDays > 0 ? (presentDays / totalDays) * 100 : 0,
      };
    });
    
    return report;
  };

  const generateActivitiesReport = () => {
    const activities = db.getActivities().filter(activity =>
      activity.date >= dateRange.start &&
      activity.date <= dateRange.end
    );
    
    const staff = db.getStaff();
    
    const report = activities.map(activity => {
      const staffMember = staff.find(member => member.id === activity.staffId);
      return {
        ...activity,
        staffName: staffMember ? `${staffMember.firstName} ${staffMember.lastName}` : 'غير محدد',
      };
    });
    
    return report;
  };

  const generateChildrenReport = () => {
    const children = db.getChildren();
    const currentDate = new Date();
    
    const report = children.map(child => {
      const birthDate = new Date(child.dateOfBirth);
      const age = currentDate.getFullYear() - birthDate.getFullYear();
      const enrollmentDate = new Date(child.enrollmentDate);
      const daysEnrolled = Math.floor((currentDate.getTime() - enrollmentDate.getTime()) / (1000 * 60 * 60 * 24));
      
      return {
        ...child,
        age,
        daysEnrolled,
      };
    });
    
    return report;
  };

  const exportToCSV = (data: any[], filename: string) => {
    if (data.length === 0) return;
    
    const headers = Object.keys(data[0]);
    const csvContent = [
      headers.join(','),
      ...data.map(row => headers.map(header => row[header]).join(','))
    ].join('\n');
    
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `${filename}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const getReportData = () => {
    switch (reportType) {
      case 'attendance':
        return generateAttendanceReport();
      case 'activities':
        return generateActivitiesReport();
      case 'children':
        return generateChildrenReport();
      default:
        return [];
    }
  };

  const reportData = getReportData();

  return (
    <div className="space-y-6">
      {/* رأس الصفحة */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">التقارير</h1>
          <p className="text-gray-600">إنشاء وتصدير التقارير المختلفة</p>
        </div>
      </div>

      {/* خيارات التقرير */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* نوع التقرير */}
          <div>
            <label className="form-label">نوع التقرير</label>
            <select
              value={reportType}
              onChange={(e) => setReportType(e.target.value as any)}
              className="form-input"
            >
              <option value="attendance">تقرير الحضور والغياب</option>
              <option value="activities">تقرير الأنشطة</option>
              <option value="children">تقرير الأطفال</option>
            </select>
          </div>

          {/* تاريخ البداية */}
          <div>
            <label className="form-label">من تاريخ</label>
            <input
              type="date"
              value={dateRange.start}
              onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
              className="form-input"
            />
          </div>

          {/* تاريخ النهاية */}
          <div>
            <label className="form-label">إلى تاريخ</label>
            <input
              type="date"
              value={dateRange.end}
              onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
              className="form-input"
            />
          </div>
        </div>

        <div className="mt-6 flex justify-end">
          <button
            onClick={() => exportToCSV(reportData, `${reportType}_report_${Date.now()}`)}
            className="btn-primary flex items-center space-x-2 space-x-reverse"
            disabled={reportData.length === 0}
          >
            <Download className="h-5 w-5" />
            <span>تصدير التقرير</span>
          </button>
        </div>
      </div>

      {/* عرض التقرير */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">
              {reportType === 'attendance' && 'تقرير الحضور والغياب'}
              {reportType === 'activities' && 'تقرير الأنشطة'}
              {reportType === 'children' && 'تقرير الأطفال'}
            </h3>
            <div className="flex items-center space-x-2 space-x-reverse text-sm text-gray-500">
              <Calendar className="h-4 w-4" />
              <span>
                {new Date(dateRange.start).toLocaleDateString('ar-SA')} - {new Date(dateRange.end).toLocaleDateString('ar-SA')}
              </span>
            </div>
          </div>
        </div>

        <div className="p-6">
          {reportData.length > 0 ? (
            <div className="overflow-x-auto">
              {reportType === 'attendance' && (
                <table className="table">
                  <thead>
                    <tr>
                      <th>اسم الطفل</th>
                      <th>إجمالي الأيام</th>
                      <th>أيام الحضور</th>
                      <th>أيام الغياب</th>
                      <th>أيام التأخير</th>
                      <th>أيام المرض</th>
                      <th>معدل الحضور</th>
                    </tr>
                  </thead>
                  <tbody>
                    {reportData.map((row: any, index) => (
                      <tr key={index}>
                        <td className="font-medium">{row.childName}</td>
                        <td>{row.totalDays}</td>
                        <td className="text-green-600">{row.presentDays}</td>
                        <td className="text-red-600">{row.absentDays}</td>
                        <td className="text-yellow-600">{row.lateDays}</td>
                        <td className="text-blue-600">{row.sickDays}</td>
                        <td>
                          <div className="flex items-center space-x-2 space-x-reverse">
                            <div className="flex-1 bg-gray-200 rounded-full h-2">
                              <div
                                className="bg-green-500 h-2 rounded-full"
                                style={{ width: `${row.attendanceRate}%` }}
                              ></div>
                            </div>
                            <span className="text-sm font-medium">
                              {row.attendanceRate.toFixed(1)}%
                            </span>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              )}

              {reportType === 'activities' && (
                <table className="table">
                  <thead>
                    <tr>
                      <th>عنوان النشاط</th>
                      <th>النوع</th>
                      <th>التاريخ</th>
                      <th>الوقت</th>
                      <th>المسؤول</th>
                      <th>الوصف</th>
                    </tr>
                  </thead>
                  <tbody>
                    {reportData.map((row: any, index) => (
                      <tr key={index}>
                        <td className="font-medium">{row.title}</td>
                        <td>
                          <span className="badge badge-info">
                            {row.type === 'educational' ? 'تعليمي' :
                             row.type === 'recreational' ? 'ترفيهي' :
                             row.type === 'meal' ? 'وجبة' :
                             row.type === 'nap' ? 'قيلولة' :
                             row.type === 'outdoor' ? 'خارجي' :
                             row.type === 'art' ? 'فني' :
                             row.type === 'music' ? 'موسيقي' : row.type}
                          </span>
                        </td>
                        <td>{new Date(row.date).toLocaleDateString('ar-SA')}</td>
                        <td>{row.startTime} - {row.endTime}</td>
                        <td>{row.staffName}</td>
                        <td className="max-w-xs truncate">{row.description}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              )}

              {reportType === 'children' && (
                <table className="table">
                  <thead>
                    <tr>
                      <th>الاسم</th>
                      <th>العمر</th>
                      <th>الجنس</th>
                      <th>ولي الأمر</th>
                      <th>رقم الهاتف</th>
                      <th>تاريخ التسجيل</th>
                      <th>أيام التسجيل</th>
                      <th>الحالة</th>
                    </tr>
                  </thead>
                  <tbody>
                    {reportData.map((row: any, index) => (
                      <tr key={index}>
                        <td className="font-medium">{row.firstName} {row.lastName}</td>
                        <td>{row.age} سنة</td>
                        <td>{row.gender === 'male' ? 'ذكر' : 'أنثى'}</td>
                        <td>{row.parentName}</td>
                        <td>{row.parentPhone}</td>
                        <td>{new Date(row.enrollmentDate).toLocaleDateString('ar-SA')}</td>
                        <td>{row.daysEnrolled} يوم</td>
                        <td>
                          <span className={`badge ${row.isActive ? 'badge-success' : 'badge-danger'}`}>
                            {row.isActive ? 'نشط' : 'غير نشط'}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              )}
            </div>
          ) : (
            <div className="text-center py-12">
              <FileText className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">لا توجد بيانات للتقرير</h3>
              <p className="mt-1 text-sm text-gray-500">
                جرب تغيير نوع التقرير أو نطاق التاريخ
              </p>
            </div>
          )}
        </div>
      </div>

      {/* إحصائيات سريعة */}
      {reportData.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">إجمالي السجلات</p>
                <p className="text-2xl font-bold text-blue-600">{reportData.length}</p>
              </div>
              <FileText className="h-8 w-8 text-blue-400" />
            </div>
          </div>

          {reportType === 'attendance' && (
            <>
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">متوسط الحضور</p>
                    <p className="text-2xl font-bold text-green-600">
                      {(reportData.reduce((sum: number, row: any) => sum + row.attendanceRate, 0) / reportData.length).toFixed(1)}%
                    </p>
                  </div>
                  <TrendingUp className="h-8 w-8 text-green-400" />
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">إجمالي أيام الحضور</p>
                    <p className="text-2xl font-bold text-purple-600">
                      {reportData.reduce((sum: number, row: any) => sum + row.presentDays, 0)}
                    </p>
                  </div>
                  <Users className="h-8 w-8 text-purple-400" />
                </div>
              </div>
            </>
          )}

          {reportType === 'activities' && (
            <>
              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">الأنشطة التعليمية</p>
                    <p className="text-2xl font-bold text-green-600">
                      {reportData.filter((row: any) => row.type === 'educational').length}
                    </p>
                  </div>
                  <Activity className="h-8 w-8 text-green-400" />
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">الأنشطة الترفيهية</p>
                    <p className="text-2xl font-bold text-purple-600">
                      {reportData.filter((row: any) => row.type === 'recreational').length}
                    </p>
                  </div>
                  <Activity className="h-8 w-8 text-purple-400" />
                </div>
              </div>
            </>
          )}
        </div>
      )}
    </div>
  );
};

export default Reports;
