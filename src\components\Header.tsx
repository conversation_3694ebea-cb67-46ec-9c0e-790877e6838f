import React from 'react';
import { <PERSON><PERSON>, <PERSON>, Search, User } from 'lucide-react';
import { db } from '../database/db';

interface HeaderProps {
  onToggleSidebar: () => void;
  sidebarOpen: boolean;
}

const Header: React.FC<HeaderProps> = ({ onToggleSidebar, sidebarOpen }) => {
  const nurseryName = db.getSetting('nursery_name')?.value || 'روضة BB للأطفال';
  const currentDate = new Date().toLocaleDateString('ar-SA', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });

  return (
    <header className="bg-white shadow-sm border-b border-gray-200">
      <div className="flex items-center justify-between px-6 py-4">
        {/* الجانب الأيمن */}
        <div className="flex items-center space-x-4 space-x-reverse">
          {/* زر القائمة */}
          <button
            onClick={onToggleSidebar}
            className="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500"
          >
            <Menu className="h-6 w-6" />
          </button>

          {/* معلومات الروضة والتاريخ */}
          <div className="text-right">
            <h2 className="text-lg font-semibold text-gray-900">{nurseryName}</h2>
            <p className="text-sm text-gray-500">{currentDate}</p>
          </div>
        </div>

        {/* الجانب الأيسر */}
        <div className="flex items-center space-x-4 space-x-reverse">
          {/* شريط البحث */}
          <div className="relative">
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="البحث..."
              className="block w-full pr-10 pl-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500 text-sm"
            />
          </div>

          {/* زر التنبيهات */}
          <button className="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500 relative">
            <Bell className="h-6 w-6" />
            {/* نقطة التنبيه */}
            <span className="absolute top-1 left-1 block h-2 w-2 rounded-full bg-red-400 ring-2 ring-white"></span>
          </button>

          {/* قائمة المستخدم */}
          <div className="relative">
            <button className="flex items-center space-x-2 space-x-reverse text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
              <div className="h-8 w-8 rounded-full bg-primary-600 flex items-center justify-center">
                <User className="h-5 w-5 text-white" />
              </div>
              <div className="text-right">
                <p className="text-sm font-medium text-gray-700">المدير</p>
                <p className="text-xs text-gray-500">مدير النظام</p>
              </div>
            </button>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
