import React, { useState, useEffect } from 'react';
import { Plus, Search, Edit, Trash2, <PERSON>, <PERSON> } from 'lucide-react';
import { db } from '../database/db';
import { Child } from '../types';
import toast from 'react-hot-toast';
// import ChildModal from '../components/ChildModal';

const Children: React.FC = () => {
  const [children, setChildren] = useState<Child[]>([]);
  const [filteredChildren, setFilteredChildren] = useState<Child[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedChild, setSelectedChild] = useState<Child | null>(null);
  const [modalMode, setModalMode] = useState<'add' | 'edit' | 'view'>('add');

  useEffect(() => {
    loadChildren();
  }, []);

  useEffect(() => {
    // تصفية الأطفال حسب البحث
    const filtered = children.filter(child =>
      child.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      child.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      child.parentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      child.parentPhone.includes(searchTerm)
    );
    setFilteredChildren(filtered);
  }, [children, searchTerm]);

  const loadChildren = () => {
    const childrenData = db.getChildren();
    setChildren(childrenData);
  };

  const handleAddChild = () => {
    setSelectedChild(null);
    setModalMode('add');
    setIsModalOpen(true);
  };

  const handleEditChild = (child: Child) => {
    setSelectedChild(child);
    setModalMode('edit');
    setIsModalOpen(true);
  };

  const handleViewChild = (child: Child) => {
    setSelectedChild(child);
    setModalMode('view');
    setIsModalOpen(true);
  };

  const handleDeleteChild = (child: Child) => {
    if (window.confirm(`هل أنت متأكد من حذف الطفل ${child.firstName} ${child.lastName}؟`)) {
      const success = db.deleteChild(child.id);
      if (success) {
        toast.success('تم حذف الطفل بنجاح');
        loadChildren();
      } else {
        toast.error('حدث خطأ أثناء حذف الطفل');
      }
    }
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    setSelectedChild(null);
    loadChildren(); // إعادة تحميل البيانات بعد الإغلاق
  };

  const getAgeInYears = (dateOfBirth: string): number => {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    
    return age;
  };

  return (
    <div className="space-y-6">
      {/* رأس الصفحة */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">إدارة الأطفال</h1>
          <p className="text-gray-600">إدارة بيانات الأطفال المسجلين في الروضة</p>
        </div>
        <button
          onClick={handleAddChild}
          className="btn-primary flex items-center space-x-2 space-x-reverse"
        >
          <Plus className="h-5 w-5" />
          <span>إضافة طفل جديد</span>
        </button>
      </div>

      {/* شريط البحث والإحصائيات */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
          {/* شريط البحث */}
          <div className="relative flex-1 max-w-md">
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="البحث عن طفل..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="form-input pr-10"
            />
          </div>

          {/* الإحصائيات السريعة */}
          <div className="flex space-x-6 space-x-reverse text-sm">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{children.length}</div>
              <div className="text-gray-600">إجمالي الأطفال</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {children.filter(child => child.isActive).length}
              </div>
              <div className="text-gray-600">الأطفال النشطون</div>
            </div>
          </div>
        </div>
      </div>

      {/* جدول الأطفال */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        {filteredChildren.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="table">
              <thead>
                <tr>
                  <th>الصورة</th>
                  <th>الاسم</th>
                  <th>العمر</th>
                  <th>الجنس</th>
                  <th>ولي الأمر</th>
                  <th>رقم الهاتف</th>
                  <th>تاريخ التسجيل</th>
                  <th>الحالة</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {filteredChildren.map((child) => (
                  <tr key={child.id}>
                    <td>
                      <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                        {child.photo ? (
                          <img
                            src={child.photo}
                            alt={`${child.firstName} ${child.lastName}`}
                            className="h-10 w-10 rounded-full object-cover"
                          />
                        ) : (
                          <Baby className="h-5 w-5 text-gray-400" />
                        )}
                      </div>
                    </td>
                    <td>
                      <div>
                        <div className="font-medium text-gray-900">
                          {child.firstName} {child.lastName}
                        </div>
                      </div>
                    </td>
                    <td>
                      <span className="text-sm text-gray-900">
                        {getAgeInYears(child.dateOfBirth)} سنة
                      </span>
                    </td>
                    <td>
                      <span className="text-sm text-gray-900">
                        {child.gender === 'male' ? 'ذكر' : 'أنثى'}
                      </span>
                    </td>
                    <td>
                      <span className="text-sm text-gray-900">{child.parentName}</span>
                    </td>
                    <td>
                      <span className="text-sm text-gray-900">{child.parentPhone}</span>
                    </td>
                    <td>
                      <span className="text-sm text-gray-900">
                        {new Date(child.enrollmentDate).toLocaleDateString('ar-SA')}
                      </span>
                    </td>
                    <td>
                      <span
                        className={`badge ${
                          child.isActive ? 'badge-success' : 'badge-danger'
                        }`}
                      >
                        {child.isActive ? 'نشط' : 'غير نشط'}
                      </span>
                    </td>
                    <td>
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <button
                          onClick={() => handleViewChild(child)}
                          className="p-1 text-blue-600 hover:text-blue-800"
                          title="عرض التفاصيل"
                        >
                          <Eye className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleEditChild(child)}
                          className="p-1 text-green-600 hover:text-green-800"
                          title="تعديل"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteChild(child)}
                          className="p-1 text-red-600 hover:text-red-800"
                          title="حذف"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="text-center py-12">
            <Baby className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">
              {searchTerm ? 'لا توجد نتائج' : 'لا يوجد أطفال مسجلون'}
            </h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchTerm 
                ? 'جرب تغيير كلمات البحث' 
                : 'ابدأ بإضافة أطفال جدد إلى النظام'
              }
            </p>
            {!searchTerm && (
              <div className="mt-6">
                <button
                  onClick={handleAddChild}
                  className="btn-primary flex items-center space-x-2 space-x-reverse mx-auto"
                >
                  <Plus className="h-5 w-5" />
                  <span>إضافة طفل جديد</span>
                </button>
              </div>
            )}
          </div>
        )}
      </div>

      {/* نافذة إضافة/تعديل الطفل */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <h2 className="text-xl font-bold mb-4">
              {modalMode === 'add' ? 'إضافة طفل جديد' :
               modalMode === 'edit' ? 'تعديل بيانات الطفل' : 'عرض بيانات الطفل'}
            </h2>
            <p className="text-gray-600 mb-4">سيتم إضافة مكون النافذة المنبثقة قريباً</p>
            <button
              onClick={handleModalClose}
              className="btn-primary"
            >
              إغلاق
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default Children;
