import React, { useState, useEffect } from 'react';
import { Calendar, UserCheck, Clock, Users } from 'lucide-react';
import { db } from '../database/db';
import { Child, Attendance as AttendanceType } from '../types';
import toast from 'react-hot-toast';

const Attendance: React.FC = () => {
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [children, setChildren] = useState<Child[]>([]);
  const [attendance, setAttendance] = useState<AttendanceType[]>([]);

  useEffect(() => {
    loadChildren();
    loadAttendance();
  }, [selectedDate]);

  const loadChildren = () => {
    const childrenData = db.getChildren().filter(child => child.isActive);
    setChildren(childrenData);
  };

  const loadAttendance = () => {
    const attendanceData = db.getAttendanceByDate(selectedDate);
    setAttendance(attendanceData);
  };

  const getChildAttendance = (childId: number): AttendanceType | undefined => {
    return attendance.find(record => record.childId === childId);
  };

  const handleAttendanceChange = (childId: number, status: 'present' | 'absent' | 'late' | 'sick') => {
    const existingRecord = getChildAttendance(childId);
    
    if (existingRecord) {
      // تحديث السجل الموجود
      const updated = db.updateAttendance(existingRecord.id, { status });
      if (updated) {
        toast.success('تم تحديث الحضور بنجاح');
        loadAttendance();
      }
    } else {
      // إضافة سجل جديد
      const newRecord = {
        childId,
        date: selectedDate,
        status,
        checkIn: status === 'present' || status === 'late' ? new Date().toTimeString().split(' ')[0] : undefined,
      };
      
      const added = db.addAttendance(newRecord);
      if (added) {
        toast.success('تم تسجيل الحضور بنجاح');
        loadAttendance();
      }
    }
  };

  const handleCheckInOut = (childId: number, type: 'checkIn' | 'checkOut') => {
    const existingRecord = getChildAttendance(childId);
    const currentTime = new Date().toTimeString().split(' ')[0];
    
    if (existingRecord) {
      const updateData = type === 'checkIn' 
        ? { checkIn: currentTime, status: 'present' as const }
        : { checkOut: currentTime };
      
      const updated = db.updateAttendance(existingRecord.id, updateData);
      if (updated) {
        toast.success(`تم تسجيل ${type === 'checkIn' ? 'الدخول' : 'الخروج'} بنجاح`);
        loadAttendance();
      }
    } else if (type === 'checkIn') {
      const newRecord = {
        childId,
        date: selectedDate,
        status: 'present' as const,
        checkIn: currentTime,
      };
      
      const added = db.addAttendance(newRecord);
      if (added) {
        toast.success('تم تسجيل الدخول بنجاح');
        loadAttendance();
      }
    }
  };

  const getAttendanceStats = () => {
    const total = children.length;
    const present = attendance.filter(record => record.status === 'present').length;
    const absent = attendance.filter(record => record.status === 'absent').length;
    const late = attendance.filter(record => record.status === 'late').length;
    const sick = attendance.filter(record => record.status === 'sick').length;
    
    return { total, present, absent, late, sick };
  };

  const stats = getAttendanceStats();

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'present': return 'text-green-600 bg-green-100';
      case 'absent': return 'text-red-600 bg-red-100';
      case 'late': return 'text-yellow-600 bg-yellow-100';
      case 'sick': return 'text-blue-600 bg-blue-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'present': return 'حاضر';
      case 'absent': return 'غائب';
      case 'late': return 'متأخر';
      case 'sick': return 'مريض';
      default: return 'غير محدد';
    }
  };

  return (
    <div className="space-y-6">
      {/* رأس الصفحة */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">الحضور والغياب</h1>
          <p className="text-gray-600">تسجيل ومتابعة حضور الأطفال يومياً</p>
        </div>
        
        {/* اختيار التاريخ */}
        <div className="flex items-center space-x-4 space-x-reverse">
          <Calendar className="h-5 w-5 text-gray-400" />
          <input
            type="date"
            value={selectedDate}
            onChange={(e) => setSelectedDate(e.target.value)}
            className="form-input"
          />
        </div>
      </div>

      {/* إحصائيات الحضور */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">إجمالي الأطفال</p>
              <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
            </div>
            <Users className="h-8 w-8 text-gray-400" />
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">حاضر</p>
              <p className="text-2xl font-bold text-green-600">{stats.present}</p>
            </div>
            <UserCheck className="h-8 w-8 text-green-400" />
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">غائب</p>
              <p className="text-2xl font-bold text-red-600">{stats.absent}</p>
            </div>
            <div className="h-8 w-8 rounded-full bg-red-100 flex items-center justify-center">
              <span className="text-red-600 font-bold">×</span>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">متأخر</p>
              <p className="text-2xl font-bold text-yellow-600">{stats.late}</p>
            </div>
            <Clock className="h-8 w-8 text-yellow-400" />
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">مريض</p>
              <p className="text-2xl font-bold text-blue-600">{stats.sick}</p>
            </div>
            <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
              <span className="text-blue-600 font-bold">+</span>
            </div>
          </div>
        </div>
      </div>

      {/* جدول الحضور */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">
            سجل الحضور - {new Date(selectedDate).toLocaleDateString('ar-SA')}
          </h3>
        </div>
        
        {children.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="table">
              <thead>
                <tr>
                  <th>الطفل</th>
                  <th>الحالة</th>
                  <th>وقت الدخول</th>
                  <th>وقت الخروج</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {children.map((child) => {
                  const attendanceRecord = getChildAttendance(child.id);
                  return (
                    <tr key={child.id}>
                      <td>
                        <div className="flex items-center space-x-3 space-x-reverse">
                          <div className="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center">
                            <span className="text-sm font-medium text-gray-600">
                              {child.firstName.charAt(0)}
                            </span>
                          </div>
                          <div>
                            <div className="font-medium text-gray-900">
                              {child.firstName} {child.lastName}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td>
                        <div className="flex space-x-2 space-x-reverse">
                          {['present', 'absent', 'late', 'sick'].map((status) => (
                            <button
                              key={status}
                              onClick={() => handleAttendanceChange(child.id, status as any)}
                              className={`px-3 py-1 rounded-full text-xs font-medium transition-colors ${
                                attendanceRecord?.status === status
                                  ? getStatusColor(status)
                                  : 'text-gray-600 bg-gray-100 hover:bg-gray-200'
                              }`}
                            >
                              {getStatusText(status)}
                            </button>
                          ))}
                        </div>
                      </td>
                      <td>
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <span className="text-sm text-gray-900">
                            {attendanceRecord?.checkIn || '-'}
                          </span>
                          {!attendanceRecord?.checkIn && (
                            <button
                              onClick={() => handleCheckInOut(child.id, 'checkIn')}
                              className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded hover:bg-green-200"
                            >
                              تسجيل دخول
                            </button>
                          )}
                        </div>
                      </td>
                      <td>
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <span className="text-sm text-gray-900">
                            {attendanceRecord?.checkOut || '-'}
                          </span>
                          {attendanceRecord?.checkIn && !attendanceRecord?.checkOut && (
                            <button
                              onClick={() => handleCheckInOut(child.id, 'checkOut')}
                              className="text-xs bg-red-100 text-red-700 px-2 py-1 rounded hover:bg-red-200"
                            >
                              تسجيل خروج
                            </button>
                          )}
                        </div>
                      </td>
                      <td>
                        <div className="text-sm text-gray-500">
                          {attendanceRecord?.notes || '-'}
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="text-center py-12">
            <UserCheck className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">لا يوجد أطفال نشطون</h3>
            <p className="mt-1 text-sm text-gray-500">أضف أطفال إلى النظام أولاً</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default Attendance;
