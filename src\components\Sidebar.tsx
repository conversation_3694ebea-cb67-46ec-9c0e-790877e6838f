import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  Home,
  Users,
  UserCheck,
  Calendar,
  Activity,
  FileText,
  Settings,
  Baby,
} from 'lucide-react';

interface SidebarProps {
  isOpen: boolean;
}

const Sidebar: React.FC<SidebarProps> = ({ isOpen }) => {
  const location = useLocation();

  const menuItems = [
    {
      path: '/dashboard',
      name: 'لوحة التحكم',
      icon: Home,
    },
    {
      path: '/children',
      name: 'الأطفال',
      icon: Baby,
    },
    {
      path: '/staff',
      name: 'الموظفين',
      icon: Users,
    },
    {
      path: '/attendance',
      name: 'الحضور والغياب',
      icon: UserCheck,
    },
    {
      path: '/activities',
      name: 'الأنشطة',
      icon: Activity,
    },
    {
      path: '/reports',
      name: 'التقارير',
      icon: FileText,
    },
    {
      path: '/settings',
      name: 'الإعدادات',
      icon: Settings,
    },
  ];

  return (
    <div
      className={`fixed inset-y-0 right-0 z-50 bg-white shadow-lg transition-all duration-300 ${
        isOpen ? 'w-64' : 'w-16'
      }`}
    >
      {/* شعار التطبيق */}
      <div className="flex items-center justify-center h-16 bg-primary-600 text-white">
        <div className="flex items-center space-x-2 space-x-reverse">
          <Baby className="h-8 w-8" />
          {isOpen && (
            <div className="text-right">
              <h1 className="text-lg font-bold">BB Creche</h1>
              <p className="text-xs opacity-90">نظام إدارة الروضة</p>
            </div>
          )}
        </div>
      </div>

      {/* قائمة التنقل */}
      <nav className="mt-8">
        <div className="px-2 space-y-1">
          {menuItems.map((item) => {
            const Icon = item.icon;
            const isActive = location.pathname === item.path;

            return (
              <Link
                key={item.path}
                to={item.path}
                className={`group flex items-center px-2 py-3 text-sm font-medium rounded-md transition-colors duration-200 ${
                  isActive
                    ? 'bg-primary-100 text-primary-700 border-l-4 border-primary-600'
                    : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                }`}
                title={!isOpen ? item.name : ''}
              >
                <Icon
                  className={`flex-shrink-0 h-5 w-5 ${
                    isActive ? 'text-primary-600' : 'text-gray-400 group-hover:text-gray-500'
                  }`}
                />
                {isOpen && (
                  <span className="mr-3 text-right">{item.name}</span>
                )}
              </Link>
            );
          })}
        </div>
      </nav>

      {/* معلومات إضافية في الأسفل */}
      {isOpen && (
        <div className="absolute bottom-0 w-full p-4 bg-gray-50 border-t">
          <div className="text-center text-xs text-gray-500">
            <p>الإصدار 1.0.0</p>
            <p className="mt-1">© 2024 BB Creche Manager</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default Sidebar;
