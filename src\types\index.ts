// أنواع البيانات الأساسية للتطبيق

export interface Child {
  id: number;
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  gender: 'male' | 'female';
  parentName: string;
  parentPhone: string;
  parentEmail?: string;
  address: string;
  medicalInfo?: string;
  allergies?: string;
  emergencyContact: string;
  emergencyPhone: string;
  photo?: string;
  enrollmentDate: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Staff {
  id: number;
  firstName: string;
  lastName: string;
  position: string;
  phone: string;
  email?: string;
  address: string;
  hireDate: string;
  salary?: number;
  qualifications?: string;
  photo?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Attendance {
  id: number;
  childId: number;
  date: string;
  checkIn?: string;
  checkOut?: string;
  status: 'present' | 'absent' | 'late' | 'sick';
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Activity {
  id: number;
  title: string;
  description: string;
  date: string;
  startTime: string;
  endTime: string;
  type: 'educational' | 'recreational' | 'meal' | 'nap' | 'outdoor' | 'art' | 'music';
  staffId: number;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface ChildActivity {
  id: number;
  childId: number;
  activityId: number;
  participation: 'excellent' | 'good' | 'fair' | 'needs_improvement';
  notes?: string;
  createdAt: string;
}

export interface Assessment {
  id: number;
  childId: number;
  staffId: number;
  date: string;
  category: 'social' | 'cognitive' | 'physical' | 'emotional' | 'language';
  score: number; // 1-5 scale
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Settings {
  id: number;
  key: string;
  value: string;
  description?: string;
  updatedAt: string;
}

// أنواع البيانات للواجهة
export interface DashboardStats {
  totalChildren: number;
  activeChildren: number;
  totalStaff: number;
  todayAttendance: number;
  attendanceRate: number;
}

export interface AttendanceReport {
  childId: number;
  childName: string;
  totalDays: number;
  presentDays: number;
  absentDays: number;
  lateDays: number;
  attendanceRate: number;
}

export interface ActivityReport {
  activityId: number;
  title: string;
  date: string;
  participantCount: number;
  averageParticipation: number;
}

// أنواع النماذج
export interface ChildFormData {
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  gender: 'male' | 'female';
  parentName: string;
  parentPhone: string;
  parentEmail?: string;
  address: string;
  medicalInfo?: string;
  allergies?: string;
  emergencyContact: string;
  emergencyPhone: string;
  photo?: File;
}

export interface StaffFormData {
  firstName: string;
  lastName: string;
  position: string;
  phone: string;
  email?: string;
  address: string;
  hireDate: string;
  salary?: number;
  qualifications?: string;
  photo?: File;
}

export interface ActivityFormData {
  title: string;
  description: string;
  date: string;
  startTime: string;
  endTime: string;
  type: 'educational' | 'recreational' | 'meal' | 'nap' | 'outdoor' | 'art' | 'music';
  staffId: number;
  notes?: string;
}

// أنواع الاستجابات
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}
