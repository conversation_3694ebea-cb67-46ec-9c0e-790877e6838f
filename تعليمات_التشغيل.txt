========================================
BB Creche Manager - نظام إدارة الروضة
========================================

تعليمات التشغيل السريع:

1. تأكد من تثبيت Node.js:
   - قم بتحميل Node.js من: https://nodejs.org
   - اختر الإصدار LTS (الموصى به)
   - قم بتثبيته واتبع التعليمات

2. تشغيل التطبيق:
   
   الطريقة الأولى (الأسهل):
   - انقر نقراً مزدوجاً على ملف "start.bat"
   
   الطريقة الثانية:
   - افتح PowerShell في مجلد التطبيق
   - اكتب: .\run-app.ps1
   
   الطريقة الثالثة (يدوياً):
   - افتح Command Prompt أو PowerShell
   - انتقل إلى مجلد التطبيق
   - اكتب: npm install
   - ثم اكتب: npm run dev

3. الوصول للتطبيق:
   - سيفتح التطبيق تلقائياً في المتصفح
   - أو اذهب إلى: http://localhost:5173

4. في حالة مواجهة مشاكل:
   - تأكد من تثبيت Node.js بشكل صحيح
   - تأكد من الاتصال بالإنترنت لتحميل التبعيات
   - قم بتشغيل PowerShell كمدير إذا لزم الأمر

========================================

المميزات الرئيسية:

✅ لوحة تحكم شاملة
✅ إدارة بيانات الأطفال
✅ إدارة الموظفين
✅ نظام الحضور والغياب
✅ إدارة الأنشطة اليومية
✅ تقارير مفصلة
✅ نظام النسخ الاحتياطي
✅ واجهة عربية بالكامل
✅ تصميم عصري وأنيق

========================================

للدعم الفني:
- راجع ملف README.md للتفاصيل الكاملة
- تحقق من وجود تحديثات جديدة

تم التطوير بـ ❤️ لخدمة رياض الأطفال
