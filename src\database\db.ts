// نظام قاعدة البيانات المبسط باستخدام localStorage للتطبيق المكتبي
import { Child, Staff, Attendance, Activity, ChildActivity, Assessment, Settings } from '../types';

class LocalDatabase {
  private storagePrefix = 'bbcreche_';

  // حفظ البيانات
  private save<T>(key: string, data: T[]): void {
    localStorage.setItem(this.storagePrefix + key, JSON.stringify(data));
  }

  // قراءة البيانات
  private load<T>(key: string): T[] {
    const data = localStorage.getItem(this.storagePrefix + key);
    return data ? JSON.parse(data) : [];
  }

  // توليد ID جديد
  private generateId(items: any[]): number {
    return items.length > 0 ? Math.max(...items.map(item => item.id)) + 1 : 1;
  }

  // تهيئة قاعدة البيانات
  initialize(): void {
    // إنشاء البيانات الافتراضية إذا لم تكن موجودة
    if (!localStorage.getItem(this.storagePrefix + 'settings')) {
      const defaultSettings: Settings[] = [
        { id: 1, key: 'nursery_name', value: 'روضة BB للأطفال', description: 'اسم الروضة', updatedAt: new Date().toISOString() },
        { id: 2, key: 'nursery_address', value: '', description: 'عنوان الروضة', updatedAt: new Date().toISOString() },
        { id: 3, key: 'nursery_phone', value: '', description: 'هاتف الروضة', updatedAt: new Date().toISOString() },
        { id: 4, key: 'working_hours_start', value: '07:00', description: 'بداية ساعات العمل', updatedAt: new Date().toISOString() },
        { id: 5, key: 'working_hours_end', value: '17:00', description: 'نهاية ساعات العمل', updatedAt: new Date().toISOString() },
      ];
      this.save('settings', defaultSettings);
    }

    // إنشاء الجداول الفارغة إذا لم تكن موجودة
    if (!localStorage.getItem(this.storagePrefix + 'children')) {
      this.save('children', []);
    }
    if (!localStorage.getItem(this.storagePrefix + 'staff')) {
      this.save('staff', []);
    }
    if (!localStorage.getItem(this.storagePrefix + 'attendance')) {
      this.save('attendance', []);
    }
    if (!localStorage.getItem(this.storagePrefix + 'activities')) {
      this.save('activities', []);
    }
    if (!localStorage.getItem(this.storagePrefix + 'child_activities')) {
      this.save('child_activities', []);
    }
    if (!localStorage.getItem(this.storagePrefix + 'assessments')) {
      this.save('assessments', []);
    }

    console.log('تم تهيئة قاعدة البيانات المحلية بنجاح');
  }

  // عمليات الأطفال
  getChildren(): Child[] {
    return this.load<Child>('children');
  }

  getChildById(id: number): Child | undefined {
    const children = this.getChildren();
    return children.find(child => child.id === id);
  }

  addChild(childData: Omit<Child, 'id' | 'createdAt' | 'updatedAt'>): Child {
    const children = this.getChildren();
    const newChild: Child = {
      ...childData,
      id: this.generateId(children),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    children.push(newChild);
    this.save('children', children);
    return newChild;
  }

  updateChild(id: number, childData: Partial<Child>): Child | null {
    const children = this.getChildren();
    const index = children.findIndex(child => child.id === id);
    if (index === -1) return null;

    children[index] = {
      ...children[index],
      ...childData,
      updatedAt: new Date().toISOString(),
    };
    this.save('children', children);
    return children[index];
  }

  deleteChild(id: number): boolean {
    const children = this.getChildren();
    const filteredChildren = children.filter(child => child.id !== id);
    if (filteredChildren.length === children.length) return false;
    
    this.save('children', filteredChildren);
    return true;
  }

  // عمليات الموظفين
  getStaff(): Staff[] {
    return this.load<Staff>('staff');
  }

  getStaffById(id: number): Staff | undefined {
    const staff = this.getStaff();
    return staff.find(member => member.id === id);
  }

  addStaff(staffData: Omit<Staff, 'id' | 'createdAt' | 'updatedAt'>): Staff {
    const staff = this.getStaff();
    const newStaff: Staff = {
      ...staffData,
      id: this.generateId(staff),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    staff.push(newStaff);
    this.save('staff', staff);
    return newStaff;
  }

  updateStaff(id: number, staffData: Partial<Staff>): Staff | null {
    const staff = this.getStaff();
    const index = staff.findIndex(member => member.id === id);
    if (index === -1) return null;

    staff[index] = {
      ...staff[index],
      ...staffData,
      updatedAt: new Date().toISOString(),
    };
    this.save('staff', staff);
    return staff[index];
  }

  deleteStaff(id: number): boolean {
    const staff = this.getStaff();
    const filteredStaff = staff.filter(member => member.id !== id);
    if (filteredStaff.length === staff.length) return false;
    
    this.save('staff', filteredStaff);
    return true;
  }

  // عمليات الحضور
  getAttendance(): Attendance[] {
    return this.load<Attendance>('attendance');
  }

  getAttendanceByDate(date: string): Attendance[] {
    const attendance = this.getAttendance();
    return attendance.filter(record => record.date === date);
  }

  getAttendanceByChild(childId: number): Attendance[] {
    const attendance = this.getAttendance();
    return attendance.filter(record => record.childId === childId);
  }

  addAttendance(attendanceData: Omit<Attendance, 'id' | 'createdAt' | 'updatedAt'>): Attendance {
    const attendance = this.getAttendance();
    const newAttendance: Attendance = {
      ...attendanceData,
      id: this.generateId(attendance),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    attendance.push(newAttendance);
    this.save('attendance', attendance);
    return newAttendance;
  }

  updateAttendance(id: number, attendanceData: Partial<Attendance>): Attendance | null {
    const attendance = this.getAttendance();
    const index = attendance.findIndex(record => record.id === id);
    if (index === -1) return null;

    attendance[index] = {
      ...attendance[index],
      ...attendanceData,
      updatedAt: new Date().toISOString(),
    };
    this.save('attendance', attendance);
    return attendance[index];
  }

  // عمليات الأنشطة
  getActivities(): Activity[] {
    return this.load<Activity>('activities');
  }

  getActivitiesByDate(date: string): Activity[] {
    const activities = this.getActivities();
    return activities.filter(activity => activity.date === date);
  }

  addActivity(activityData: Omit<Activity, 'id' | 'createdAt' | 'updatedAt'>): Activity {
    const activities = this.getActivities();
    const newActivity: Activity = {
      ...activityData,
      id: this.generateId(activities),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    activities.push(newActivity);
    this.save('activities', activities);
    return newActivity;
  }

  // عمليات الإعدادات
  getSettings(): Settings[] {
    return this.load<Settings>('settings');
  }

  getSetting(key: string): Settings | undefined {
    const settings = this.getSettings();
    return settings.find(setting => setting.key === key);
  }

  updateSetting(key: string, value: string): Settings | null {
    const settings = this.getSettings();
    const index = settings.findIndex(setting => setting.key === key);
    if (index === -1) return null;

    settings[index] = {
      ...settings[index],
      value,
      updatedAt: new Date().toISOString(),
    };
    this.save('settings', settings);
    return settings[index];
  }

  // إحصائيات سريعة
  getStats() {
    const children = this.getChildren();
    const staff = this.getStaff();
    const today = new Date().toISOString().split('T')[0];
    const todayAttendance = this.getAttendanceByDate(today);

    return {
      totalChildren: children.length,
      activeChildren: children.filter(child => child.isActive).length,
      totalStaff: staff.filter(member => member.isActive).length,
      todayAttendance: todayAttendance.filter(record => record.status === 'present').length,
      attendanceRate: todayAttendance.length > 0 
        ? (todayAttendance.filter(record => record.status === 'present').length / todayAttendance.length) * 100 
        : 0,
    };
  }

  // نسخ احتياطي
  exportData(): string {
    const data = {
      children: this.getChildren(),
      staff: this.getStaff(),
      attendance: this.getAttendance(),
      activities: this.getActivities(),
      settings: this.getSettings(),
      exportDate: new Date().toISOString(),
    };
    return JSON.stringify(data, null, 2);
  }

  // استيراد البيانات
  importData(jsonData: string): boolean {
    try {
      const data = JSON.parse(jsonData);
      
      if (data.children) this.save('children', data.children);
      if (data.staff) this.save('staff', data.staff);
      if (data.attendance) this.save('attendance', data.attendance);
      if (data.activities) this.save('activities', data.activities);
      if (data.settings) this.save('settings', data.settings);
      
      return true;
    } catch (error) {
      console.error('خطأ في استيراد البيانات:', error);
      return false;
    }
  }
}

// إنشاء مثيل واحد من قاعدة البيانات
export const db = new LocalDatabase();
export default db;
