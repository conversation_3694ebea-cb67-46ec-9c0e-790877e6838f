# BB Creche Manager - نظام إدارة الروضة
Write-Host "========================================" -ForegroundColor Green
Write-Host "BB Creche Manager - نظام إدارة الروضة" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# تحقق من وجود Node.js
try {
    $nodeVersion = node --version
    Write-Host "Node.js version: $nodeVersion" -ForegroundColor Yellow
} catch {
    Write-Host "خطأ: Node.js غير مثبت. يرجى تثبيت Node.js أولاً" -ForegroundColor Red
    Write-Host "يمكنك تحميله من: https://nodejs.org" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج"
    exit
}

# تحقق من وجود npm
try {
    $npmVersion = npm --version
    Write-Host "npm version: $npmVersion" -ForegroundColor Yellow
} catch {
    Write-Host "خطأ: npm غير متوفر" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج"
    exit
}

Write-Host ""
Write-Host "جاري تثبيت التبعيات..." -ForegroundColor Cyan

# تثبيت التبعيات
try {
    npm install
    Write-Host "تم تثبيت التبعيات بنجاح!" -ForegroundColor Green
} catch {
    Write-Host "خطأ في تثبيت التبعيات" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج"
    exit
}

Write-Host ""
Write-Host "جاري تشغيل التطبيق..." -ForegroundColor Cyan
Write-Host "سيتم فتح التطبيق في المتصفح تلقائياً" -ForegroundColor Yellow
Write-Host ""

# تشغيل التطبيق
try {
    npm run dev
} catch {
    Write-Host "خطأ في تشغيل التطبيق" -ForegroundColor Red
    Read-Host "اضغط Enter للخروج"
}
