import React, { useState, useEffect } from 'react';
import { Save, Download, Upload, Settings as SettingsIcon, Database } from 'lucide-react';
import { db } from '../database/db';
import { Settings as SettingsType } from '../types';
import toast from 'react-hot-toast';

const Settings: React.FC = () => {
  const [settings, setSettings] = useState<SettingsType[]>([]);
  const [formData, setFormData] = useState<Record<string, string>>({});

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = () => {
    const settingsData = db.getSettings();
    setSettings(settingsData);
    
    // تحويل الإعدادات إلى كائن للنموذج
    const formObject: Record<string, string> = {};
    settingsData.forEach(setting => {
      formObject[setting.key] = setting.value;
    });
    setFormData(formObject);
  };

  const handleInputChange = (key: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleSaveSettings = () => {
    try {
      Object.entries(formData).forEach(([key, value]) => {
        db.updateSetting(key, value);
      });
      toast.success('تم حفظ الإعدادات بنجاح');
      loadSettings();
    } catch (error) {
      toast.error('حدث خطأ أثناء حفظ الإعدادات');
    }
  };

  const handleExportData = () => {
    try {
      const data = db.exportData();
      const blob = new Blob([data], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `bbcreche_backup_${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      toast.success('تم تصدير البيانات بنجاح');
    } catch (error) {
      toast.error('حدث خطأ أثناء تصدير البيانات');
    }
  };

  const handleImportData = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const jsonData = e.target?.result as string;
        const success = db.importData(jsonData);
        if (success) {
          toast.success('تم استيراد البيانات بنجاح');
          loadSettings();
          // إعادة تحميل الصفحة لتحديث جميع البيانات
          setTimeout(() => {
            window.location.reload();
          }, 1000);
        } else {
          toast.error('فشل في استيراد البيانات');
        }
      } catch (error) {
        toast.error('ملف البيانات غير صالح');
      }
    };
    reader.readAsText(file);
  };

  const getSettingLabel = (key: string) => {
    switch (key) {
      case 'nursery_name': return 'اسم الروضة';
      case 'nursery_address': return 'عنوان الروضة';
      case 'nursery_phone': return 'هاتف الروضة';
      case 'nursery_email': return 'البريد الإلكتروني';
      case 'working_hours_start': return 'بداية ساعات العمل';
      case 'working_hours_end': return 'نهاية ساعات العمل';
      case 'backup_frequency': return 'تكرار النسخ الاحتياطي';
      case 'language': return 'اللغة';
      default: return key;
    }
  };

  const getInputType = (key: string) => {
    switch (key) {
      case 'nursery_email': return 'email';
      case 'nursery_phone': return 'tel';
      case 'working_hours_start':
      case 'working_hours_end': return 'time';
      default: return 'text';
    }
  };

  return (
    <div className="space-y-6">
      {/* رأس الصفحة */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">الإعدادات</h1>
          <p className="text-gray-600">إدارة إعدادات النظام والنسخ الاحتياطي</p>
        </div>
      </div>

      {/* إعدادات الروضة */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center space-x-2 space-x-reverse">
            <SettingsIcon className="h-5 w-5" />
            <span>إعدادات الروضة</span>
          </h3>
        </div>
        
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {settings.map((setting) => (
              <div key={setting.id}>
                <label className="form-label">
                  {getSettingLabel(setting.key)}
                </label>
                <input
                  type={getInputType(setting.key)}
                  value={formData[setting.key] || ''}
                  onChange={(e) => handleInputChange(setting.key, e.target.value)}
                  className="form-input"
                  placeholder={setting.description}
                />
                {setting.description && (
                  <p className="text-sm text-gray-500 mt-1">{setting.description}</p>
                )}
              </div>
            ))}
          </div>
          
          <div className="mt-6 flex justify-end">
            <button
              onClick={handleSaveSettings}
              className="btn-primary flex items-center space-x-2 space-x-reverse"
            >
              <Save className="h-5 w-5" />
              <span>حفظ الإعدادات</span>
            </button>
          </div>
        </div>
      </div>

      {/* النسخ الاحتياطي */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center space-x-2 space-x-reverse">
            <Database className="h-5 w-5" />
            <span>النسخ الاحتياطي</span>
          </h3>
        </div>
        
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* تصدير البيانات */}
            <div className="border border-gray-200 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-2">تصدير البيانات</h4>
              <p className="text-sm text-gray-600 mb-4">
                قم بتصدير جميع بيانات النظام كنسخة احتياطية
              </p>
              <button
                onClick={handleExportData}
                className="btn-secondary flex items-center space-x-2 space-x-reverse w-full justify-center"
              >
                <Download className="h-5 w-5" />
                <span>تصدير البيانات</span>
              </button>
            </div>

            {/* استيراد البيانات */}
            <div className="border border-gray-200 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-2">استيراد البيانات</h4>
              <p className="text-sm text-gray-600 mb-4">
                قم باستيراد البيانات من نسخة احتياطية سابقة
              </p>
              <div className="relative">
                <input
                  type="file"
                  accept=".json"
                  onChange={handleImportData}
                  className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                />
                <button className="btn-warning flex items-center space-x-2 space-x-reverse w-full justify-center">
                  <Upload className="h-5 w-5" />
                  <span>استيراد البيانات</span>
                </button>
              </div>
            </div>
          </div>

          {/* تحذير */}
          <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="mr-3">
                <h3 className="text-sm font-medium text-yellow-800">تحذير مهم</h3>
                <div className="mt-2 text-sm text-yellow-700">
                  <p>
                    • قم بإنشاء نسخة احتياطية بانتظام لحماية بياناتك
                  </p>
                  <p>
                    • استيراد البيانات سيحل محل جميع البيانات الحالية
                  </p>
                  <p>
                    • تأكد من صحة ملف النسخة الاحتياطية قبل الاستيراد
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* معلومات النظام */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">معلومات النظام</h3>
        </div>
        
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {db.getChildren().length}
              </div>
              <div className="text-sm text-gray-600">إجمالي الأطفال</div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {db.getStaff().length}
              </div>
              <div className="text-sm text-gray-600">إجمالي الموظفين</div>
            </div>
            
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {db.getActivities().length}
              </div>
              <div className="text-sm text-gray-600">إجمالي الأنشطة</div>
            </div>
          </div>
          
          <div className="mt-6 pt-6 border-t border-gray-200 text-center text-sm text-gray-500">
            <p>نظام إدارة روضة الأطفال BB Creche Manager</p>
            <p>الإصدار 1.0.0 - تم التطوير بواسطة فريق BB</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Settings;
