import React, { useState, useEffect } from 'react';
import { Plus, Search, Edit, Trash2, Eye, Users } from 'lucide-react';
import { db } from '../database/db';
import { Staff } from '../types';
import toast from 'react-hot-toast';

const StaffPage: React.FC = () => {
  const [staff, setStaff] = useState<Staff[]>([]);
  const [filteredStaff, setFilteredStaff] = useState<Staff[]>([]);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    loadStaff();
  }, []);

  useEffect(() => {
    const filtered = staff.filter(member =>
      member.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      member.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      member.position.toLowerCase().includes(searchTerm.toLowerCase()) ||
      member.phone.includes(searchTerm)
    );
    setFilteredStaff(filtered);
  }, [staff, searchTerm]);

  const loadStaff = () => {
    const staffData = db.getStaff();
    setStaff(staffData);
  };

  const handleAddStaff = () => {
    // إضافة موظف تجريبي
    const newStaff = {
      firstName: 'فاطمة',
      lastName: 'أحمد',
      position: 'معلمة',
      phone: '0501234567',
      email: '<EMAIL>',
      address: 'الرياض',
      hireDate: new Date().toISOString().split('T')[0],
      salary: 5000,
      qualifications: 'بكالوريوس تربية',
      isActive: true,
    };

    const addedStaff = db.addStaff(newStaff);
    if (addedStaff) {
      toast.success('تم إضافة الموظف بنجاح');
      loadStaff();
    }
  };

  const handleDeleteStaff = (member: Staff) => {
    if (window.confirm(`هل أنت متأكد من حذف الموظف ${member.firstName} ${member.lastName}؟`)) {
      const success = db.deleteStaff(member.id);
      if (success) {
        toast.success('تم حذف الموظف بنجاح');
        loadStaff();
      } else {
        toast.error('حدث خطأ أثناء حذف الموظف');
      }
    }
  };

  return (
    <div className="space-y-6">
      {/* رأس الصفحة */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">إدارة الموظفين</h1>
          <p className="text-gray-600">إدارة بيانات المعلمات والموظفين في الروضة</p>
        </div>
        <button
          onClick={handleAddStaff}
          className="btn-primary flex items-center space-x-2 space-x-reverse"
        >
          <Plus className="h-5 w-5" />
          <span>إضافة موظف جديد</span>
        </button>
      </div>

      {/* شريط البحث والإحصائيات */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
          <div className="relative flex-1 max-w-md">
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="البحث عن موظف..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="form-input pr-10"
            />
          </div>

          <div className="flex space-x-6 space-x-reverse text-sm">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{staff.length}</div>
              <div className="text-gray-600">إجمالي الموظفين</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {staff.filter(member => member.isActive).length}
              </div>
              <div className="text-gray-600">الموظفون النشطون</div>
            </div>
          </div>
        </div>
      </div>

      {/* جدول الموظفين */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        {filteredStaff.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="table">
              <thead>
                <tr>
                  <th>الصورة</th>
                  <th>الاسم</th>
                  <th>المنصب</th>
                  <th>رقم الهاتف</th>
                  <th>البريد الإلكتروني</th>
                  <th>تاريخ التوظيف</th>
                  <th>الراتب</th>
                  <th>الحالة</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {filteredStaff.map((member) => (
                  <tr key={member.id}>
                    <td>
                      <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                        {member.photo ? (
                          <img
                            src={member.photo}
                            alt={`${member.firstName} ${member.lastName}`}
                            className="h-10 w-10 rounded-full object-cover"
                          />
                        ) : (
                          <Users className="h-5 w-5 text-gray-400" />
                        )}
                      </div>
                    </td>
                    <td>
                      <div className="font-medium text-gray-900">
                        {member.firstName} {member.lastName}
                      </div>
                    </td>
                    <td>
                      <span className="text-sm text-gray-900">{member.position}</span>
                    </td>
                    <td>
                      <span className="text-sm text-gray-900">{member.phone}</span>
                    </td>
                    <td>
                      <span className="text-sm text-gray-900">{member.email || '-'}</span>
                    </td>
                    <td>
                      <span className="text-sm text-gray-900">
                        {new Date(member.hireDate).toLocaleDateString('ar-SA')}
                      </span>
                    </td>
                    <td>
                      <span className="text-sm text-gray-900">
                        {member.salary ? `${member.salary} ريال` : '-'}
                      </span>
                    </td>
                    <td>
                      <span
                        className={`badge ${
                          member.isActive ? 'badge-success' : 'badge-danger'
                        }`}
                      >
                        {member.isActive ? 'نشط' : 'غير نشط'}
                      </span>
                    </td>
                    <td>
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <button
                          className="p-1 text-blue-600 hover:text-blue-800"
                          title="عرض التفاصيل"
                        >
                          <Eye className="h-4 w-4" />
                        </button>
                        <button
                          className="p-1 text-green-600 hover:text-green-800"
                          title="تعديل"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteStaff(member)}
                          className="p-1 text-red-600 hover:text-red-800"
                          title="حذف"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="text-center py-12">
            <Users className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">
              {searchTerm ? 'لا توجد نتائج' : 'لا يوجد موظفون مسجلون'}
            </h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchTerm 
                ? 'جرب تغيير كلمات البحث' 
                : 'ابدأ بإضافة موظفين جدد إلى النظام'
              }
            </p>
            {!searchTerm && (
              <div className="mt-6">
                <button
                  onClick={handleAddStaff}
                  className="btn-primary flex items-center space-x-2 space-x-reverse mx-auto"
                >
                  <Plus className="h-5 w-5" />
                  <span>إضافة موظف جديد</span>
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default StaffPage;
