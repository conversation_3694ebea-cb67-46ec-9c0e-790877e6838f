import React, { useState, useEffect } from 'react';
import { Plus, Calendar, Clock, Users, Activity as ActivityIcon } from 'lucide-react';
import { db } from '../database/db';
import { Activity, Staff } from '../types';
import toast from 'react-hot-toast';

const Activities: React.FC = () => {
  const [activities, setActivities] = useState<Activity[]>([]);
  const [staff, setStaff] = useState<Staff[]>([]);
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);

  useEffect(() => {
    loadActivities();
    loadStaff();
  }, [selectedDate]);

  const loadActivities = () => {
    const activitiesData = db.getActivitiesByDate(selectedDate);
    setActivities(activitiesData);
  };

  const loadStaff = () => {
    const staffData = db.getStaff().filter(member => member.isActive);
    setStaff(staffData);
  };

  const handleAddActivity = () => {
    // إضافة نشاط تجريبي
    const newActivity = {
      title: 'نشاط تعليمي',
      description: 'نشاط تعليمي للأطفال',
      date: selectedDate,
      startTime: '09:00',
      endTime: '10:00',
      type: 'educational' as const,
      staffId: staff.length > 0 ? staff[0].id : 1,
      notes: 'نشاط تجريبي',
    };

    const added = db.addActivity(newActivity);
    if (added) {
      toast.success('تم إضافة النشاط بنجاح');
      loadActivities();
    }
  };

  const getActivityTypeText = (type: string) => {
    switch (type) {
      case 'educational': return 'تعليمي';
      case 'recreational': return 'ترفيهي';
      case 'meal': return 'وجبة';
      case 'nap': return 'قيلولة';
      case 'outdoor': return 'خارجي';
      case 'art': return 'فني';
      case 'music': return 'موسيقي';
      default: return type;
    }
  };

  const getActivityTypeColor = (type: string) => {
    switch (type) {
      case 'educational': return 'bg-blue-100 text-blue-800';
      case 'recreational': return 'bg-green-100 text-green-800';
      case 'meal': return 'bg-orange-100 text-orange-800';
      case 'nap': return 'bg-purple-100 text-purple-800';
      case 'outdoor': return 'bg-yellow-100 text-yellow-800';
      case 'art': return 'bg-pink-100 text-pink-800';
      case 'music': return 'bg-indigo-100 text-indigo-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStaffName = (staffId: number) => {
    const staffMember = staff.find(member => member.id === staffId);
    return staffMember ? `${staffMember.firstName} ${staffMember.lastName}` : 'غير محدد';
  };

  return (
    <div className="space-y-6">
      {/* رأس الصفحة */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">الأنشطة</h1>
          <p className="text-gray-600">إدارة الأنشطة اليومية للأطفال</p>
        </div>
        
        <div className="flex items-center space-x-4 space-x-reverse">
          {/* اختيار التاريخ */}
          <div className="flex items-center space-x-2 space-x-reverse">
            <Calendar className="h-5 w-5 text-gray-400" />
            <input
              type="date"
              value={selectedDate}
              onChange={(e) => setSelectedDate(e.target.value)}
              className="form-input"
            />
          </div>
          
          <button
            onClick={handleAddActivity}
            className="btn-primary flex items-center space-x-2 space-x-reverse"
          >
            <Plus className="h-5 w-5" />
            <span>إضافة نشاط</span>
          </button>
        </div>
      </div>

      {/* إحصائيات الأنشطة */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">أنشطة اليوم</p>
              <p className="text-2xl font-bold text-blue-600">{activities.length}</p>
            </div>
            <ActivityIcon className="h-8 w-8 text-blue-400" />
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">أنشطة تعليمية</p>
              <p className="text-2xl font-bold text-green-600">
                {activities.filter(activity => activity.type === 'educational').length}
              </p>
            </div>
            <div className="h-8 w-8 rounded-full bg-green-100 flex items-center justify-center">
              <span className="text-green-600 font-bold">📚</span>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">أنشطة ترفيهية</p>
              <p className="text-2xl font-bold text-purple-600">
                {activities.filter(activity => activity.type === 'recreational').length}
              </p>
            </div>
            <div className="h-8 w-8 rounded-full bg-purple-100 flex items-center justify-center">
              <span className="text-purple-600 font-bold">🎮</span>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">الموظفون المشاركون</p>
              <p className="text-2xl font-bold text-orange-600">{staff.length}</p>
            </div>
            <Users className="h-8 w-8 text-orange-400" />
          </div>
        </div>
      </div>

      {/* قائمة الأنشطة */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">
            أنشطة يوم {new Date(selectedDate).toLocaleDateString('ar-SA')}
          </h3>
        </div>
        
        {activities.length > 0 ? (
          <div className="divide-y divide-gray-200">
            {activities.map((activity) => (
              <div key={activity.id} className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 space-x-reverse mb-2">
                      <h4 className="text-lg font-medium text-gray-900">{activity.title}</h4>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getActivityTypeColor(activity.type)}`}>
                        {getActivityTypeText(activity.type)}
                      </span>
                    </div>
                    
                    <p className="text-gray-600 mb-3">{activity.description}</p>
                    
                    <div className="flex items-center space-x-6 space-x-reverse text-sm text-gray-500">
                      <div className="flex items-center space-x-1 space-x-reverse">
                        <Clock className="h-4 w-4" />
                        <span>{activity.startTime} - {activity.endTime}</span>
                      </div>
                      
                      <div className="flex items-center space-x-1 space-x-reverse">
                        <Users className="h-4 w-4" />
                        <span>المسؤول: {getStaffName(activity.staffId)}</span>
                      </div>
                    </div>
                    
                    {activity.notes && (
                      <div className="mt-3 p-3 bg-gray-50 rounded-md">
                        <p className="text-sm text-gray-600">
                          <strong>ملاحظات:</strong> {activity.notes}
                        </p>
                      </div>
                    )}
                  </div>
                  
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <button className="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-md">
                      <ActivityIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <ActivityIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">لا توجد أنشطة لهذا اليوم</h3>
            <p className="mt-1 text-sm text-gray-500">ابدأ بإضافة أنشطة جديدة للأطفال</p>
            <div className="mt-6">
              <button
                onClick={handleAddActivity}
                className="btn-primary flex items-center space-x-2 space-x-reverse mx-auto"
              >
                <Plus className="h-5 w-5" />
                <span>إضافة نشاط جديد</span>
              </button>
            </div>
          </div>
        )}
      </div>

      {/* جدولة الأنشطة */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">الجدول الزمني</h3>
        </div>
        
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {activities
              .sort((a, b) => a.startTime.localeCompare(b.startTime))
              .map((activity) => (
                <div key={activity.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-900">
                      {activity.startTime} - {activity.endTime}
                    </span>
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getActivityTypeColor(activity.type)}`}>
                      {getActivityTypeText(activity.type)}
                    </span>
                  </div>
                  <h4 className="font-medium text-gray-900 mb-1">{activity.title}</h4>
                  <p className="text-sm text-gray-600">{getStaffName(activity.staffId)}</p>
                </div>
              ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Activities;
