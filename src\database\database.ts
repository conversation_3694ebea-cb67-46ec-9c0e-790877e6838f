// استخدام sqlite3 بدلاً من better-sqlite3 للتوافق مع Electron
const sqlite3 = require('sqlite3').verbose();
import path from 'path';
import fs from 'fs';
import { app } from 'electron';

class DatabaseManager {
  private db: any = null;
  private dbPath: string;

  constructor() {
    // تحديد مسار قاعدة البيانات في مجلد التطبيق
    this.dbPath = path.join(process.cwd(), 'data', 'bbcreche.db');

    // التأكد من وجود المجلد
    const dbDir = path.dirname(this.dbPath);
    if (!fs.existsSync(dbDir)) {
      fs.mkdirSync(dbDir, { recursive: true });
    }
  }

  // الاتصال بقاعدة البيانات
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.db = new sqlite3.Database(this.dbPath, (err: any) => {
          if (err) {
            console.error('خطأ في الاتصال بقاعدة البيانات:', err);
            reject(err);
          } else {
            console.log('تم الاتصال بقاعدة البيانات بنجاح');
            this.db.run('PRAGMA foreign_keys = ON');
            this.initializeTables().then(resolve).catch(reject);
          }
        });
      } catch (error) {
        console.error('خطأ في الاتصال بقاعدة البيانات:', error);
        reject(error);
      }
    });
  }

  // إنشاء الجداول
  private initializeTables(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error('قاعدة البيانات غير متصلة'));
        return;
      }

    // جدول الأطفال
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS children (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        firstName TEXT NOT NULL,
        lastName TEXT NOT NULL,
        dateOfBirth TEXT NOT NULL,
        gender TEXT CHECK(gender IN ('male', 'female')) NOT NULL,
        parentName TEXT NOT NULL,
        parentPhone TEXT NOT NULL,
        parentEmail TEXT,
        address TEXT NOT NULL,
        medicalInfo TEXT,
        allergies TEXT,
        emergencyContact TEXT NOT NULL,
        emergencyPhone TEXT NOT NULL,
        photo TEXT,
        enrollmentDate TEXT NOT NULL,
        isActive BOOLEAN DEFAULT 1,
        createdAt TEXT DEFAULT CURRENT_TIMESTAMP,
        updatedAt TEXT DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // جدول الموظفين
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS staff (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        firstName TEXT NOT NULL,
        lastName TEXT NOT NULL,
        position TEXT NOT NULL,
        phone TEXT NOT NULL,
        email TEXT,
        address TEXT NOT NULL,
        hireDate TEXT NOT NULL,
        salary REAL,
        qualifications TEXT,
        photo TEXT,
        isActive BOOLEAN DEFAULT 1,
        createdAt TEXT DEFAULT CURRENT_TIMESTAMP,
        updatedAt TEXT DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // جدول الحضور
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS attendance (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        childId INTEGER NOT NULL,
        date TEXT NOT NULL,
        checkIn TEXT,
        checkOut TEXT,
        status TEXT CHECK(status IN ('present', 'absent', 'late', 'sick')) NOT NULL,
        notes TEXT,
        createdAt TEXT DEFAULT CURRENT_TIMESTAMP,
        updatedAt TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (childId) REFERENCES children (id) ON DELETE CASCADE,
        UNIQUE(childId, date)
      )
    `);

    // جدول الأنشطة
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS activities (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT NOT NULL,
        description TEXT NOT NULL,
        date TEXT NOT NULL,
        startTime TEXT NOT NULL,
        endTime TEXT NOT NULL,
        type TEXT CHECK(type IN ('educational', 'recreational', 'meal', 'nap', 'outdoor', 'art', 'music')) NOT NULL,
        staffId INTEGER NOT NULL,
        notes TEXT,
        createdAt TEXT DEFAULT CURRENT_TIMESTAMP,
        updatedAt TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (staffId) REFERENCES staff (id) ON DELETE CASCADE
      )
    `);

    // جدول مشاركة الأطفال في الأنشطة
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS child_activities (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        childId INTEGER NOT NULL,
        activityId INTEGER NOT NULL,
        participation TEXT CHECK(participation IN ('excellent', 'good', 'fair', 'needs_improvement')) NOT NULL,
        notes TEXT,
        createdAt TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (childId) REFERENCES children (id) ON DELETE CASCADE,
        FOREIGN KEY (activityId) REFERENCES activities (id) ON DELETE CASCADE,
        UNIQUE(childId, activityId)
      )
    `);

    // جدول التقييمات
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS assessments (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        childId INTEGER NOT NULL,
        staffId INTEGER NOT NULL,
        date TEXT NOT NULL,
        category TEXT CHECK(category IN ('social', 'cognitive', 'physical', 'emotional', 'language')) NOT NULL,
        score INTEGER CHECK(score >= 1 AND score <= 5) NOT NULL,
        notes TEXT,
        createdAt TEXT DEFAULT CURRENT_TIMESTAMP,
        updatedAt TEXT DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (childId) REFERENCES children (id) ON DELETE CASCADE,
        FOREIGN KEY (staffId) REFERENCES staff (id) ON DELETE CASCADE
      )
    `);

    // جدول الإعدادات
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        key TEXT UNIQUE NOT NULL,
        value TEXT NOT NULL,
        description TEXT,
        updatedAt TEXT DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // إنشاء الفهارس لتحسين الأداء
    this.db.exec(`
      CREATE INDEX IF NOT EXISTS idx_children_active ON children(isActive);
      CREATE INDEX IF NOT EXISTS idx_attendance_date ON attendance(date);
      CREATE INDEX IF NOT EXISTS idx_attendance_child ON attendance(childId);
      CREATE INDEX IF NOT EXISTS idx_activities_date ON activities(date);
      CREATE INDEX IF NOT EXISTS idx_assessments_child ON assessments(childId);
    `);

    // إدراج الإعدادات الافتراضية
    this.insertDefaultSettings();

    console.log('تم إنشاء جداول قاعدة البيانات بنجاح');
  }

  // إدراج الإعدادات الافتراضية
  private insertDefaultSettings(): void {
    if (!this.db) return;

    const defaultSettings = [
      { key: 'nursery_name', value: 'روضة BB للأطفال', description: 'اسم الروضة' },
      { key: 'nursery_address', value: '', description: 'عنوان الروضة' },
      { key: 'nursery_phone', value: '', description: 'هاتف الروضة' },
      { key: 'nursery_email', value: '', description: 'بريد الروضة الإلكتروني' },
      { key: 'working_hours_start', value: '07:00', description: 'بداية ساعات العمل' },
      { key: 'working_hours_end', value: '17:00', description: 'نهاية ساعات العمل' },
      { key: 'backup_frequency', value: 'daily', description: 'تكرار النسخ الاحتياطي' },
      { key: 'language', value: 'ar', description: 'لغة التطبيق' },
    ];

    const insertSetting = this.db.prepare(`
      INSERT OR IGNORE INTO settings (key, value, description) 
      VALUES (?, ?, ?)
    `);

    for (const setting of defaultSettings) {
      insertSetting.run(setting.key, setting.value, setting.description);
    }
  }

  // الحصول على قاعدة البيانات
  getDatabase(): Database.Database {
    if (!this.db) {
      throw new Error('قاعدة البيانات غير متصلة');
    }
    return this.db;
  }

  // إغلاق الاتصال
  close(): void {
    if (this.db) {
      this.db.close();
      this.db = null;
      console.log('تم إغلاق الاتصال بقاعدة البيانات');
    }
  }

  // إنشاء نسخة احتياطية
  backup(backupPath: string): void {
    if (!this.db) throw new Error('قاعدة البيانات غير متصلة');
    
    try {
      this.db.backup(backupPath);
      console.log(`تم إنشاء نسخة احتياطية في: ${backupPath}`);
    } catch (error) {
      console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
      throw error;
    }
  }

  // استعادة من نسخة احتياطية
  restore(backupPath: string): void {
    if (!fs.existsSync(backupPath)) {
      throw new Error('ملف النسخة الاحتياطية غير موجود');
    }

    try {
      this.close();
      fs.copyFileSync(backupPath, this.dbPath);
      this.connect();
      console.log('تم استعادة النسخة الاحتياطية بنجاح');
    } catch (error) {
      console.error('خطأ في استعادة النسخة الاحتياطية:', error);
      throw error;
    }
  }
}

// إنشاء مثيل واحد من مدير قاعدة البيانات
export const databaseManager = new DatabaseManager();
export default databaseManager;
