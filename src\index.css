@tailwind base;
@tailwind components;
@tailwind utilities;

/* إعدادات الخط العربي */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');

:root {
  font-family: 'Cairo', Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
  font-family: 'Cairo', sans-serif;
  direction: rtl;
  text-align: right;
}

#root {
  width: 100%;
  height: 100vh;
  margin: 0 auto;
  text-align: center;
}

/* تخصيص الألوان للوضع الفاتح */
@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
}

/* تخصيص شريط التمرير */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* تحسينات للنصوص العربية */
.arabic-text {
  font-family: 'Cairo', sans-serif;
  direction: rtl;
  text-align: right;
}

/* تحسينات للأزرار */
.btn-primary {
  @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

.btn-secondary {
  @apply bg-secondary-600 hover:bg-secondary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

.btn-success {
  @apply bg-success-600 hover:bg-success-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

.btn-warning {
  @apply bg-warning-600 hover:bg-warning-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

.btn-danger {
  @apply bg-danger-600 hover:bg-danger-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
}

/* تحسينات للبطاقات */
.card {
  @apply bg-white rounded-lg shadow-md border border-gray-200 p-6;
}

.card-header {
  @apply border-b border-gray-200 pb-4 mb-4;
}

/* تحسينات للنماذج */
.form-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

.form-error {
  @apply text-danger-600 text-sm mt-1;
}

/* تحسينات للجداول */
.table {
  @apply w-full border-collapse bg-white rounded-lg overflow-hidden shadow-sm;
}

.table th {
  @apply bg-gray-50 px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b border-gray-200;
}

.table td {
  @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900 border-b border-gray-200;
}

.table tbody tr:hover {
  @apply bg-gray-50;
}

/* تحسينات للتنبيهات */
.alert {
  @apply p-4 rounded-lg mb-4;
}

.alert-success {
  @apply bg-success-50 border border-success-200 text-success-800;
}

.alert-warning {
  @apply bg-warning-50 border border-warning-200 text-warning-800;
}

.alert-danger {
  @apply bg-danger-50 border border-danger-200 text-danger-800;
}

.alert-info {
  @apply bg-secondary-50 border border-secondary-200 text-secondary-800;
}

/* تحسينات للشارات */
.badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.badge-success {
  @apply bg-success-100 text-success-800;
}

.badge-warning {
  @apply bg-warning-100 text-warning-800;
}

.badge-danger {
  @apply bg-danger-100 text-danger-800;
}

.badge-info {
  @apply bg-secondary-100 text-secondary-800;
}

/* تحسينات للرسوم المتحركة */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

.bounce-in {
  animation: bounceIn 0.6s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
}

@keyframes bounceIn {
  0% { transform: scale(0.3); opacity: 0; }
  50% { transform: scale(1.05); }
  70% { transform: scale(0.9); }
  100% { transform: scale(1); opacity: 1; }
}
