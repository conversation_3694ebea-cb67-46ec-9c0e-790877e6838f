# BB Creche Manager - نظام إدارة روضة الأطفال

نظام إدارة روضة أطفال احترافي ومتكامل بتصميم عصري وأنيق، مطور باستخدام Electron و React و TypeScript.

## المميزات

### 🏠 لوحة التحكم الرئيسية
- عرض الإحصائيات العامة للروضة
- متابعة معدلات الحضور اليومية
- عرض الأنشطة الحديثة
- إحصائيات سريعة ومفيدة

### 👶 إدارة الأطفال
- إضافة وتعديل بيانات الأطفال
- تخزين المعلومات الشخصية والطبية
- إدارة بيانات أولياء الأمور
- رفع وإدارة صور الأطفال
- تتبع تواريخ التسجيل والحالة

### 👩‍🏫 إدارة الموظفين
- إدارة بيانات المعلمات والموظفين
- تتبع المؤهلات والخبرات
- إدارة الرواتب وتواريخ التوظيف
- تحديد الأدوار والمسؤوليات

### 📅 نظام الحضور والغياب
- تسجيل الحضور اليومي للأطفال
- تتبع أوقات الدخول والخروج
- إحصائيات الحضور والغياب
- تصنيف حالات الغياب (مرض، إجازة، إلخ)

### 🎨 إدارة الأنشطة
- جدولة الأنشطة اليومية
- تصنيف الأنشطة (تعليمية، ترفيهية، فنية، إلخ)
- تعيين المسؤولين عن الأنشطة
- متابعة مشاركة الأطفال

### 📊 نظام التقارير
- تقارير الحضور والغياب
- تقارير الأنشطة والفعاليات
- تقارير شاملة عن الأطفال
- تصدير التقارير بصيغة CSV

### ⚙️ الإعدادات والنسخ الاحتياطي
- إعدادات الروضة العامة
- نظام النسخ الاحتياطي للبيانات
- استيراد وتصدير البيانات
- إعدادات ساعات العمل

## التقنيات المستخدمة

- **Frontend**: React 18 + TypeScript
- **Desktop Framework**: Electron
- **Styling**: Tailwind CSS
- **Icons**: Lucide React
- **Database**: LocalStorage (قابل للترقية إلى SQLite)
- **Build Tool**: Vite
- **State Management**: React Hooks

## متطلبات النظام

- Node.js 16 أو أحدث
- npm أو yarn
- Windows 10/11, macOS 10.14+, أو Linux

## التثبيت والتشغيل

### 1. تثبيت التبعيات

```bash
# إذا كان لديك مشكلة في تشغيل npm، قم بتشغيل هذا الأمر أولاً
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# ثم قم بتثبيت التبعيات
npm install
```

### 2. تشغيل التطبيق في وضع التطوير

```bash
# تشغيل خادم التطوير
npm run dev
```

### 3. بناء التطبيق للإنتاج

```bash
# بناء التطبيق
npm run build

# إنشاء ملف التثبيت
npm run dist
```

### 4. بناء التطبيق لنظام تشغيل محدد

```bash
# Windows
npm run dist:win

# macOS
npm run dist:mac

# Linux
npm run dist:linux
```

## هيكل المشروع

```
BBCreche/
├── src/
│   ├── components/          # المكونات القابلة لإعادة الاستخدام
│   │   ├── Header.tsx
│   │   └── Sidebar.tsx
│   ├── pages/              # صفحات التطبيق
│   │   ├── Dashboard.tsx
│   │   ├── Children.tsx
│   │   ├── Staff.tsx
│   │   ├── Attendance.tsx
│   │   ├── Activities.tsx
│   │   ├── Reports.tsx
│   │   └── Settings.tsx
│   ├── database/           # طبقة قاعدة البيانات
│   │   └── db.ts
│   ├── types/              # تعريفات TypeScript
│   │   └── index.ts
│   ├── utils/              # الأدوات المساعدة
│   ├── hooks/              # React Hooks المخصصة
│   ├── assets/             # الملفات الثابتة
│   ├── App.tsx             # المكون الرئيسي
│   ├── main.tsx            # نقطة دخول React
│   └── index.css           # الأنماط الرئيسية
├── electron/               # ملفات Electron
│   └── main.ts             # العملية الرئيسية لـ Electron
├── public/                 # الملفات العامة
├── package.json            # تبعيات المشروع
├── tsconfig.json           # إعدادات TypeScript
├── tailwind.config.js      # إعدادات Tailwind CSS
├── vite.config.ts          # إعدادات Vite
└── README.md               # هذا الملف
```

## الاستخدام

### إضافة طفل جديد
1. انتقل إلى صفحة "الأطفال"
2. اضغط على "إضافة طفل جديد"
3. املأ البيانات المطلوبة
4. احفظ البيانات

### تسجيل الحضور
1. انتقل إلى صفحة "الحضور والغياب"
2. اختر التاريخ المطلوب
3. حدد حالة كل طفل (حاضر/غائب/متأخر/مريض)
4. سجل أوقات الدخول والخروج

### إنشاء تقرير
1. انتقل إلى صفحة "التقارير"
2. اختر نوع التقرير المطلوب
3. حدد نطاق التاريخ
4. اضغط على "تصدير التقرير"

### النسخ الاحتياطي
1. انتقل إلى صفحة "الإعدادات"
2. في قسم "النسخ الاحتياطي"
3. اضغط على "تصدير البيانات" لإنشاء نسخة احتياطية
4. استخدم "استيراد البيانات" لاستعادة نسخة احتياطية

## المساهمة

نرحب بالمساهمات! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push إلى الفرع (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## الدعم

إذا واجهت أي مشاكل أو لديك اقتراحات، يرجى فتح issue في GitHub أو التواصل معنا.

## الإصدارات القادمة

- [ ] إضافة نظام المراسلات مع أولياء الأمور
- [ ] تطبيق جوال مصاحب
- [ ] نظام إدارة الوجبات
- [ ] تقارير مالية متقدمة
- [ ] نظام إدارة الأحداث والفعاليات
- [ ] دعم عدة لغات
- [ ] نظام إشعارات متقدم

---

**تم التطوير بـ ❤️ لخدمة رياض الأطفال**
