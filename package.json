{"name": "bb-creche-manager", "version": "1.0.0", "description": "نظام إدارة روضة أطفال احترافي ومتكامل", "main": "dist/main.js", "homepage": "./", "author": "BB Creche Management System", "license": "MIT", "private": true, "scripts": {"dev": "concurrently \"npm run dev:react\" \"npm run dev:electron\"", "dev:react": "vite", "dev:electron": "wait-on http://localhost:5173 && electron .", "build": "npm run build:react && npm run build:electron", "build:react": "vite build", "build:electron": "tsc -p tsconfig.electron.json", "dist": "npm run build && electron-builder", "dist:win": "npm run build && electron-builder --win", "dist:mac": "npm run build && electron-builder --mac", "dist:linux": "npm run build && electron-builder --linux", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "type-check": "tsc --noEmit"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "sqlite3": "^5.1.6", "electron-store": "^8.1.0", "date-fns": "^2.30.0", "lucide-react": "^0.294.0", "recharts": "^2.8.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/node": "^20.9.0", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "concurrently": "^8.2.2", "electron": "^27.1.2", "electron-builder": "^24.6.4", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^4.5.0", "wait-on": "^7.2.0"}, "build": {"appId": "com.bbcreche.manager", "productName": "BB Creche Manager", "directories": {"output": "release"}, "files": ["dist/**/*", "node_modules/**/*"], "mac": {"category": "public.app-category.education"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}}