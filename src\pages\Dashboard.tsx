import React, { useEffect, useState } from 'react';
import { Users, Baby, UserCheck, Activity, TrendingUp, Calendar } from 'lucide-react';
import { db } from '../database/db';
import { DashboardStats } from '../types';

const Dashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats>({
    totalChildren: 0,
    activeChildren: 0,
    totalStaff: 0,
    todayAttendance: 0,
    attendanceRate: 0,
  });

  const [recentActivities, setRecentActivities] = useState<any[]>([]);

  useEffect(() => {
    // تحميل الإحصائيات
    const dashboardStats = db.getStats();
    setStats(dashboardStats);

    // تحميل الأنشطة الحديثة
    const activities = db.getActivities().slice(-5).reverse();
    setRecentActivities(activities);
  }, []);

  const statCards = [
    {
      title: 'إجمالي الأطفال',
      value: stats.totalChildren,
      icon: Baby,
      color: 'bg-blue-500',
      textColor: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: 'الأطفال النشطون',
      value: stats.activeChildren,
      icon: Users,
      color: 'bg-green-500',
      textColor: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      title: 'الموظفون',
      value: stats.totalStaff,
      icon: UserCheck,
      color: 'bg-purple-500',
      textColor: 'text-purple-600',
      bgColor: 'bg-purple-50',
    },
    {
      title: 'حضور اليوم',
      value: stats.todayAttendance,
      icon: Calendar,
      color: 'bg-orange-500',
      textColor: 'text-orange-600',
      bgColor: 'bg-orange-50',
    },
  ];

  return (
    <div className="space-y-6">
      {/* ترحيب */}
      <div className="bg-gradient-to-l from-primary-600 to-primary-800 rounded-lg p-6 text-white">
        <h1 className="text-2xl font-bold mb-2">مرحباً بك في نظام إدارة الروضة</h1>
        <p className="text-primary-100">
          تابع أداء الروضة وإدارة الأطفال والموظفين من خلال لوحة التحكم هذه
        </p>
      </div>

      {/* بطاقات الإحصائيات */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((card, index) => {
          const Icon = card.icon;
          return (
            <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">{card.title}</p>
                  <p className="text-3xl font-bold text-gray-900">{card.value}</p>
                </div>
                <div className={`p-3 rounded-full ${card.bgColor}`}>
                  <Icon className={`h-6 w-6 ${card.textColor}`} />
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* معدل الحضور */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">معدل الحضور اليوم</h3>
          <TrendingUp className="h-5 w-5 text-green-500" />
        </div>
        <div className="flex items-center">
          <div className="flex-1 bg-gray-200 rounded-full h-3 mr-4">
            <div
              className="bg-green-500 h-3 rounded-full transition-all duration-500"
              style={{ width: `${stats.attendanceRate}%` }}
            ></div>
          </div>
          <span className="text-lg font-semibold text-gray-900">
            {stats.attendanceRate.toFixed(1)}%
          </span>
        </div>
        <p className="text-sm text-gray-600 mt-2">
          {stats.todayAttendance} من أصل {stats.activeChildren} طفل حاضر اليوم
        </p>
      </div>

      {/* الأنشطة الحديثة */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">الأنشطة الحديثة</h3>
        </div>
        <div className="p-6">
          {recentActivities.length > 0 ? (
            <div className="space-y-4">
              {recentActivities.map((activity) => (
                <div key={activity.id} className="flex items-center space-x-4 space-x-reverse">
                  <div className="flex-shrink-0">
                    <div className="h-10 w-10 rounded-full bg-primary-100 flex items-center justify-center">
                      <Activity className="h-5 w-5 text-primary-600" />
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {activity.title}
                    </p>
                    <p className="text-sm text-gray-500">
                      {activity.date} - {activity.startTime}
                    </p>
                  </div>
                  <div className="flex-shrink-0">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      activity.type === 'educational' ? 'bg-blue-100 text-blue-800' :
                      activity.type === 'recreational' ? 'bg-green-100 text-green-800' :
                      activity.type === 'meal' ? 'bg-orange-100 text-orange-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {activity.type === 'educational' ? 'تعليمي' :
                       activity.type === 'recreational' ? 'ترفيهي' :
                       activity.type === 'meal' ? 'وجبة' :
                       activity.type === 'nap' ? 'قيلولة' :
                       activity.type === 'outdoor' ? 'خارجي' :
                       activity.type === 'art' ? 'فني' :
                       activity.type === 'music' ? 'موسيقي' : activity.type}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <Activity className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">لا توجد أنشطة</h3>
              <p className="mt-1 text-sm text-gray-500">ابدأ بإضافة أنشطة جديدة للأطفال</p>
            </div>
          )}
        </div>
      </div>

      {/* إحصائيات سريعة إضافية */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* الأطفال الجدد هذا الشهر */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">الأطفال الجدد هذا الشهر</h3>
          <div className="text-center">
            <div className="text-3xl font-bold text-primary-600 mb-2">
              {db.getChildren().filter(child => {
                const enrollmentDate = new Date(child.enrollmentDate);
                const currentMonth = new Date().getMonth();
                const currentYear = new Date().getFullYear();
                return enrollmentDate.getMonth() === currentMonth && 
                       enrollmentDate.getFullYear() === currentYear;
              }).length}
            </div>
            <p className="text-sm text-gray-600">طفل جديد</p>
          </div>
        </div>

        {/* الأنشطة هذا الأسبوع */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">الأنشطة هذا الأسبوع</h3>
          <div className="text-center">
            <div className="text-3xl font-bold text-green-600 mb-2">
              {db.getActivities().filter(activity => {
                const activityDate = new Date(activity.date);
                const weekAgo = new Date();
                weekAgo.setDate(weekAgo.getDate() - 7);
                return activityDate >= weekAgo;
              }).length}
            </div>
            <p className="text-sm text-gray-600">نشاط</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
